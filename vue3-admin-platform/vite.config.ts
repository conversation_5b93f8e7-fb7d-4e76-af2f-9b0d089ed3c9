import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import UnoCSS from '@unocss/vite'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [
    vue(),
    vueDevTools(),
    UnoCSS(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
    server: {
      host: env.VITE_DEV_HOST || '0.0.0.0', // 允许内网访问
      port: parseInt(env.VITE_DEV_PORT) || 5173, // 开发服务器端口
      open: true, // 自动打开浏览器
      cors: true, // 允许跨域
      proxy: {
        // 代理配置 - 将 /api 请求代理到数据源服务器 (8081端口)
        '/api': {
          target: 'http://127.0.0.1:8081', // 数据源服务器地址
          changeOrigin: true, // 改变请求头中的origin字段
          rewrite: (path) => path.replace(/^\/api/, '/api') // 保持 /api 前缀
        }
      }
    },
    build: {
      target: 'esnext',
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: {
            'naive-ui': ['naive-ui'],
            'vue-vendor': ['vue', 'vue-router', 'pinia']
          }
        }
      }
    }
  }
})
