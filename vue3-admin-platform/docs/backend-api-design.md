# 数据库连接管理系统 - 后端API设计

## 🏗️ 系统架构

### 技术栈建议
- **后端框架**: Node.js + Express / Python + FastAPI / Java + Spring Boot
- **数据库连接池**: 支持多种数据库的连接池管理
- **安全**: JWT认证 + 连接信息加密存储
- **缓存**: Redis缓存连接状态和查询结果

## 📡 API接口设计

### 1. 数据库连接管理

#### 1.1 测试数据库连接
```http
POST /api/database/test-connection
Content-Type: application/json

{
  "type": "mysql",
  "host": "localhost",
  "port": 3306,
  "username": "root",
  "password": "password",
  "database": "test_db",
  "options": "charset=utf8mb4&timeout=30"
}

Response:
{
  "success": true,
  "message": "连接成功",
  "serverInfo": {
    "version": "8.0.25",
    "charset": "utf8mb4"
  }
}
```

#### 1.2 保存数据库连接配置
```http
POST /api/database/connections
Content-Type: application/json

{
  "name": "生产环境MySQL",
  "type": "mysql",
  "host": "*************",
  "port": 3306,
  "username": "admin",
  "password": "encrypted_password",
  "database": "production_db",
  "options": "charset=utf8mb4"
}

Response:
{
  "success": true,
  "connectionId": "conn_123456",
  "message": "连接配置保存成功"
}
```

#### 1.3 获取连接列表
```http
GET /api/database/connections

Response:
{
  "success": true,
  "connections": [
    {
      "id": "conn_123456",
      "name": "生产环境MySQL",
      "type": "mysql",
      "host": "*************",
      "port": 3306,
      "username": "admin",
      "database": "production_db",
      "status": "connected",
      "lastConnected": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 2. 数据库操作

#### 2.1 获取数据库列表
```http
GET /api/database/{connectionId}/databases

Response:
{
  "success": true,
  "databases": [
    "information_schema",
    "mysql",
    "performance_schema",
    "production_db",
    "test_db"
  ]
}
```

#### 2.2 获取数据表列表
```http
GET /api/database/{connectionId}/tables?database=production_db

Response:
{
  "success": true,
  "tables": [
    {
      "name": "users",
      "rows": 1250,
      "size": "2.5MB",
      "engine": "InnoDB",
      "comment": "用户表"
    },
    {
      "name": "orders",
      "rows": 5680,
      "size": "12.3MB",
      "engine": "InnoDB",
      "comment": "订单表"
    }
  ]
}
```

#### 2.3 获取表结构
```http
GET /api/database/{connectionId}/schema?database=production_db&table=users

Response:
{
  "success": true,
  "schema": [
    {
      "column_name": "id",
      "data_type": "bigint",
      "is_nullable": "NO",
      "column_default": null,
      "extra": "auto_increment",
      "column_comment": "主键ID"
    },
    {
      "column_name": "username",
      "data_type": "varchar",
      "character_maximum_length": 50,
      "is_nullable": "NO",
      "column_default": null,
      "column_comment": "用户名"
    }
  ]
}
```

### 3. SQL查询执行

#### 3.1 执行SQL查询
```http
POST /api/database/{connectionId}/query
Content-Type: application/json

{
  "database": "production_db",
  "sql": "SELECT * FROM users WHERE created_at >= ? LIMIT ?",
  "parameters": ["2024-01-01", 100],
  "timeout": 30
}

Response:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "columns": [
    {"name": "id", "type": "bigint"},
    {"name": "username", "type": "varchar"},
    {"name": "email", "type": "varchar"},
    {"name": "created_at", "type": "datetime"}
  ],
  "rowCount": 1,
  "executionTime": 125,
  "affectedRows": 0
}
```

### 4. SQL模板管理

#### 4.1 保存SQL模板
```http
POST /api/sql-templates
Content-Type: application/json

{
  "name": "用户活跃度统计",
  "description": "统计指定时间范围内的用户活跃度",
  "category": "report",
  "sql": "SELECT DATE(login_time) as date, COUNT(DISTINCT user_id) as active_users FROM user_logs WHERE login_time BETWEEN {{start_date}} AND {{end_date}} GROUP BY DATE(login_time)",
  "parameters": [
    {
      "name": "start_date",
      "type": "date",
      "label": "开始日期",
      "required": true
    },
    {
      "name": "end_date", 
      "type": "date",
      "label": "结束日期",
      "required": true
    }
  ],
  "connectionId": "conn_123456"
}

Response:
{
  "success": true,
  "templateId": "tpl_789012",
  "message": "模板保存成功"
}
```

#### 4.2 发布模板给运营人员
```http
POST /api/sql-templates/{templateId}/publish

Response:
{
  "success": true,
  "message": "模板已发布给运营人员"
}
```

## 🔒 安全设计

### 1. 连接信息加密
```javascript
// 密码加密存储
const crypto = require('crypto');

function encryptPassword(password, secretKey) {
  const cipher = crypto.createCipher('aes-256-cbc', secretKey);
  let encrypted = cipher.update(password, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decryptPassword(encryptedPassword, secretKey) {
  const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
  let decrypted = decipher.update(encryptedPassword, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
```

### 2. SQL注入防护
```javascript
// 参数化查询
function executeQuery(connection, sql, parameters) {
  // 使用预处理语句防止SQL注入
  return connection.execute(sql, parameters);
}

// SQL语句验证
function validateSQL(sql) {
  const dangerousKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE'];
  const upperSQL = sql.toUpperCase();
  
  for (const keyword of dangerousKeywords) {
    if (upperSQL.includes(keyword)) {
      throw new Error(`不允许执行包含 ${keyword} 的SQL语句`);
    }
  }
}
```

### 3. 权限控制
```javascript
// 用户权限检查
function checkPermission(userId, action, resourceId) {
  // 检查用户是否有执行特定操作的权限
  // action: 'read', 'write', 'execute', 'manage'
  // resourceId: 连接ID或模板ID
}

// 连接访问控制
function checkConnectionAccess(userId, connectionId) {
  // 检查用户是否有访问特定数据库连接的权限
}
```

## 🗄️ 数据库设计

### 1. 连接配置表
```sql
CREATE TABLE database_connections (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type ENUM('mysql', 'postgresql', 'sqlserver', 'oracle', 'sqlite') NOT NULL,
  host VARCHAR(255) NOT NULL,
  port INT NOT NULL,
  username VARCHAR(100) NOT NULL,
  password TEXT NOT NULL, -- 加密存储
  database_name VARCHAR(100),
  options TEXT,
  status ENUM('connected', 'disconnected', 'error') DEFAULT 'disconnected',
  created_by BIGINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_created_by (created_by),
  INDEX idx_status (status)
);
```

### 2. SQL模板表
```sql
CREATE TABLE sql_templates (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  category ENUM('query', 'report', 'analytics') NOT NULL,
  sql_content TEXT NOT NULL,
  parameters JSON,
  connection_id VARCHAR(50),
  is_published BOOLEAN DEFAULT FALSE,
  usage_count INT DEFAULT 0,
  created_by BIGINT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (connection_id) REFERENCES database_connections(id),
  INDEX idx_category (category),
  INDEX idx_published (is_published),
  INDEX idx_created_by (created_by)
);
```

### 3. 查询历史表
```sql
CREATE TABLE query_history (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  connection_id VARCHAR(50) NOT NULL,
  template_id VARCHAR(50),
  sql_content TEXT NOT NULL,
  parameters JSON,
  execution_time INT, -- 毫秒
  row_count INT,
  status ENUM('success', 'error') NOT NULL,
  error_message TEXT,
  executed_by BIGINT NOT NULL,
  executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (connection_id) REFERENCES database_connections(id),
  FOREIGN KEY (template_id) REFERENCES sql_templates(id),
  INDEX idx_connection (connection_id),
  INDEX idx_executed_by (executed_by),
  INDEX idx_executed_at (executed_at)
);
```

## 🚀 部署建议

### 1. 环境变量配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=admin
DB_PASSWORD=password
DB_DATABASE=sql_manager

# 加密密钥
ENCRYPTION_SECRET_KEY=your-secret-key-here

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h
```

### 2. Docker部署
```dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

这个设计提供了完整的数据库连接管理功能，支持多种数据库类型，具备良好的安全性和扩展性。您觉得这个设计如何？需要我详细说明某个部分吗？
