# API接口对接文档

## 📋 已完成的接口对接

### 1. 数据库连接管理接口

#### 1.1 获取全部连接列表
- **接口**: `GET /api/connections`
- **前端方法**: `getConnectionList()`
- **返回**: `DatabaseConnection[]`
- **状态**: ✅ 已完成

```typescript
// 调用示例
const connections = await getConnectionList()
```

#### 1.2 获取单个连接信息
- **接口**: `GET /api/connections/{id}`
- **前端方法**: `getConnection(id: number)`
- **返回**: `DatabaseConnection`
- **状态**: ✅ 已完成

```typescript
// 调用示例
const connection = await getConnection(1)
```

#### 1.3 保存连接信息
- **接口**: `POST /api/connections`
- **前端方法**: `saveConnection(data: CreateConnectionRequest)`
- **返回**: `DatabaseConnection`
- **状态**: ✅ 已完成

```typescript
// 调用示例
const newConnection = await saveConnection({
  name: "测试连接",
  url: "mysql://localhost:3306/test",
  username: "root",
  password: "password",
  dbType: "mysql",
  group: "test"
})
```

#### 1.4 更新连接信息
- **接口**: `PUT /api/connections/{id}`
- **前端方法**: `updateConnection(data: UpdateConnectionRequest)`
- **返回**: `DatabaseConnection`
- **状态**: ✅ 已完成

#### 1.5 删除连接
- **接口**: `DELETE /api/connections/{id}`
- **前端方法**: `deleteConnection(id: number)`
- **返回**: `void`
- **状态**: ✅ 已完成

#### 1.6 测试连接
- **接口**: `POST /api/connections/test`
- **前端方法**: `testConnection(data: DatabaseConnection)`
- **返回**: `TestConnectionResponse`
- **状态**: ✅ 已完成

```typescript
// 调用示例
const result = await testConnection(connection)
if (result.success) {
  console.log('连接成功:', result.message)
}
```

## 🔧 配置说明

### 1. 代理配置
在 `vite.config.ts` 中配置了开发环境的代理：

```typescript
server: {
  host: '0.0.0.0',
  port: 5173,
  proxy: {
    '/api': {
      target: 'http://127.0.0.1:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '/api')
    }
  }
}
```

### 2. 环境变量
- `.env.development`: 开发环境配置
- `.env.production`: 生产环境配置
- `.env.example`: 配置示例文件

### 3. API基础配置
```typescript
// API基础URL
const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api'
const API_TIMEOUT = import.meta.env.VITE_API_TIMEOUT || 30000
```

## 🧪 测试工具

### API测试组件
在开发环境下，数据库连接管理页面会显示API测试工具，包含：

1. **健康检查**: 检查后端API是否可用
2. **连接列表测试**: 测试获取连接列表接口
3. **连接测试**: 测试指定连接的连通性
4. **创建连接测试**: 测试创建新连接

### 使用方法
1. 启动后端服务 (http://127.0.0.1:8080)
2. 启动前端开发服务器 (`pnpm dev`)
3. 访问数据库连接管理页面
4. 使用页面顶部的API测试工具进行调试

## 📊 数据结构

### DatabaseConnection
```typescript
interface DatabaseConnection {
  id: number                    // 连接ID
  name: string                  // 连接名称
  url: string                   // 数据库URL
  username: string              // 用户名
  password?: string             // 密码(前端通常不返回)
  dbType: string               // 数据库类型
  createTime: string           // 创建时间
  lastUseTime?: string         // 最后使用时间
  group?: string               // 分组
  status?: ConnectionStatus    // 连接状态(前端计算)
}
```

### CreateConnectionRequest
```typescript
interface CreateConnectionRequest {
  name: string
  url: string
  username: string
  password: string
  dbType: string
  group?: string
}
```

## 🚀 启动说明

### 前端启动
```bash
cd vue3-admin-platform
pnpm install
pnpm dev
```

### 后端要求
- 后端服务需要运行在 `http://127.0.0.1:8080`
- 需要实现上述API接口
- 返回数据格式需要与前端类型定义一致

## 🔍 调试技巧

1. **查看API配置**: 开发环境下会在控制台打印API配置信息
2. **健康检查**: 使用API测试工具检查后端服务状态
3. **网络面板**: 使用浏览器开发者工具查看API请求和响应
4. **错误处理**: 前端会在API调用失败时回退到模拟数据

## ⚠️ 注意事项

1. **CORS**: 已在Vite配置中启用CORS支持
2. **超时**: API请求默认超时时间为30秒
3. **错误处理**: 所有API调用都包含错误处理和用户提示
4. **类型安全**: 使用TypeScript确保类型安全
5. **环境区分**: 开发和生产环境使用不同的配置
