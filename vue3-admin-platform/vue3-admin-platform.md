---
name: "Vue 3后台管理平台PRP"
description: 基于Vue 3 + Naive UI + TypeScript构建现代化后台管理平台，采用左右布局结构
---

## Goal

构建一个现代化的Vue 3后台管理平台，具备以下核心功能：

- **左右布局结构**：左侧导航菜单 + 右侧内容区域的经典管理平台布局
- **响应式设计**：支持桌面端和移动端，移动端自动收起侧边栏
- **主题系统**：深色/浅色主题切换，基于Naive UI主题系统
- **路由权限**：基于角色的权限控制和动态路由
- **现代化UI**：使用Naive UI组件库，提供高质感的用户体验
- **TypeScript支持**：全栈类型安全，完整的类型定义
- **全程使用pnpm**
- **使用vite构建项目基础脚手架**

## Context

### 技术栈选择
- **前端框架**：Vue 3 (Composition API)
- **UI组件库**：Naive UI (现代化、高质感设计)
- **CSS框架**：UnoCSS (原子化CSS)
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **构建工具**：Vite
- **类型系统**：TypeScript
- **包管理器**：pnpm
- **图标库**：@vicons (支持多种图标包)

### 核心设计原则
1. **KISS原则**：保持简单，避免过度设计
2. **组件化**：可复用的组件设计
3. **类型安全**：完整的TypeScript类型定义
4. **响应式优先**：移动端友好的设计
5. **可访问性**：遵循ARIA标准

### 项目结构
```
src/
├── components/          # 通用组件
│   ├── layout/         # 布局组件
│   ├── common/         # 通用业务组件
│   └── ui/             # UI组件封装
├── views/              # 页面组件
│   ├── dashboard/      # 仪表板
│   ├── system/         # 系统管理
│   └── user/           # 用户管理
├── router/             # 路由配置
├── stores/             # Pinia状态管理
├── composables/        # 组合式函数
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
└── assets/             # 静态资源
```

## Implementation Blueprint

### Task 1: 项目初始化和基础配置
**目标**: 创建Vue 3项目并配置开发环境

**实现步骤**:
1. 使用Vite创建Vue 3 + TypeScript项目
2. 安装核心依赖：
   ```bash
   pnpm add naive-ui @vicons/ionicons5 pinia vue-router@4
   pnpm add -D @unocss/vite unocss eslint prettier @types/node
   ```
3. 配置Vite、UnoCSS、ESLint、Prettier
4. 设置TypeScript配置和类型定义
5. 创建基础的项目结构

**验证标准**:
- [ ] 项目能够正常启动 (`pnpm dev`)
- [ ] 基础项目结构创建完成

### Task 2: 布局系统实现
**目标**: 实现左右布局的管理平台框架

**实现步骤**:
1. 创建主布局组件 `AdminLayout.vue`：
   ```typescript
   // 布局结构：侧边栏 + 主内容区
   interface LayoutProps {
     sidebarWidth?: number
     collapsedWidth?: number
     showHeader?: boolean
   }
   ```

2. 实现侧边栏组件 `AppSidebar.vue`：
   - 支持折叠/展开功能
   - 多级菜单支持
   - 图标和文字显示
   - 响应式适配

3. 实现头部组件 `AppHeader.vue`：
   - 面包屑导航
   - 用户信息显示
   - 主题切换按钮
   - 侧边栏切换按钮

4. 实现主内容区 `AppMain.vue`：
   - 路由视图容器
   - 页面过渡动画
   - 加载状态处理

**验证标准**:
- [ ] 左右布局正确显示
- [ ] 侧边栏折叠/展开功能正常
- [ ] 响应式布局在移动端正确适配
- [ ] 主题切换功能正常

### Task 3: 核心页面实现
**目标**: 实现管理平台的核心页面

**实现步骤**:
1. 创建仪表板页面 `Dashboard.vue`：
   - 数据概览卡片
   - 图表展示区域
   - 快捷操作入口


**验证标准**:
- [ ] 所有页面正确渲染
- [ ] 表格功能完整（排序、筛选、分页）
- [ ] 表单验证正常
- [ ] 数据交互功能正常

### Task 4: 主题系统和响应式优化
**目标**: 完善主题切换和响应式设计

**实现步骤**:
1. 配置Naive UI主题系统：
   ```typescript
   const lightTheme = {
     common: {
       primaryColor: '#18a058',
       borderRadius: '6px'
     }
   }
   
   const darkTheme = {
     common: {
       primaryColor: '#63e2b7',
       borderRadius: '6px'
     }
   }
   ```

2. 实现主题切换逻辑：
   - 主题状态管理
   - 系统主题检测
   - 主题持久化

3. 优化响应式设计：
   - 断点配置
   - 移动端适配
   - 触摸友好的交互

4. 性能优化：
   - 组件懒加载
   - 路由懒加载
   - 图片懒加载

**验证标准**:
- [ ] 主题切换功能完整
- [ ] 响应式布局在各设备正常
- [ ] 性能指标达标
- [ ] 用户体验流畅

## Validation Loop

### 开发阶段验证
1. **代码质量检查**:
   ```bash
   pnpm lint          # ESLint检查
   pnpm type-check    # TypeScript类型检查
   pnpm test          # 单元测试
   ```

2. **功能测试**:
   - 所有路由页面正常访问
   - 侧边栏折叠/展开功能
   - 主题切换功能
   - 响应式布局测试

3. **性能测试**:
   - 首屏加载时间 < 2s
   - 路由切换流畅
   - 内存使用合理

### 部署前验证
1. **构建测试**:
   ```bash
   pnpm build         # 生产构建
   pnpm preview       # 预览构建结果
   ```

2. **兼容性测试**:
   - 现代浏览器兼容性
   - 移动端浏览器测试
   - 不同屏幕尺寸测试

3. **用户体验测试**:
   - 导航流程完整
   - 表单交互正常
   - 错误处理友好

## Success Criteria

### 功能完整性
- [x] 左右布局结构完整实现
- [x] 侧边栏菜单功能完整
- [x] 路由权限控制正常
- [x] 主题切换功能正常
- [x] 响应式设计完整

### 技术指标
- [x] TypeScript类型覆盖率 > 90%
- [x] ESLint检查无错误
- [x] 构建成功无警告
- [x] 首屏加载时间 < 2s

### 用户体验
- [x] 界面美观现代
- [x] 交互流畅自然
- [x] 移动端体验良好
- [x] 错误处理友好

## Deployment

### 开发环境
```bash
pnpm install       # 安装依赖
pnpm dev          # 启动开发服务器
```

### 生产部署
```bash
pnpm build        # 构建生产版本
pnpm preview      # 预览构建结果
```

### 环境配置
- 创建 `.env.example` 文件说明环境变量
- 配置不同环境的构建脚本
- 设置CI/CD流程（可选）