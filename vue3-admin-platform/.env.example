# 环境配置示例文件
# 复制此文件为 .env.local 并根据实际情况修改配置

# 应用配置
VITE_APP_TITLE=Vue3 Admin Platform
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=/api
VITE_API_TIMEOUT=30000

# 后端服务地址（开发环境用于代理配置）
VITE_BACKEND_URL=http://127.0.0.1:8080

# 开发服务器配置
VITE_DEV_PORT=5173
VITE_DEV_HOST=0.0.0.0

# 调试配置
VITE_DEBUG=true
VITE_MOCK_ENABLED=false

# 数据库连接配置（如果需要）
# VITE_DEFAULT_DB_HOST=localhost
# VITE_DEFAULT_DB_PORT=3306
