/**
 * 基于 axios 的统一请求工具类
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ResponseData } from '@/types'
import { getAuthToken } from '@/api/config'

/**
 * 请求配置接口
 */
export interface RequestConfig extends AxiosRequestConfig {
  // 可以扩展自定义配置
}

/**
 * 请求工具类
 */
class RequestUtil {
  private instance: AxiosInstance

  constructor(config?: RequestConfig) {
    // 创建 axios 实例
    this.instance = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...config
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 自动添加全局token（如果没有手动设置Authorization头）
        if (!config.headers?.Authorization) {
          const token = getAuthToken()
          if (token && config.headers) {
            config.headers.Authorization = token
          }
        }

        console.log('Request:', config.method?.toUpperCase(), config.url)
        return config
      },
      (error) => {
        console.error('Request Error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log('Response:', response.status, response.config.url)
        return response
      },
      (error) => {
        console.error('Response Error:', error)
        
        // 统一错误处理
        const errorResponse: ResponseData = {
          success: false,
          errCode: error.response?.status || -1,
          message: error.message || '请求失败',
          data: null
        }

        // 根据错误状态码处理
        if (error.response) {
          switch (error.response.status) {
            case 401:
              errorResponse.message = '未授权，请重新登录'
              break
            case 403:
              errorResponse.message = '权限不足'
              break
            case 404:
              errorResponse.message = '请求的资源不存在'
              break
            case 500:
              errorResponse.message = '服务器内部错误'
              break
            default:
              errorResponse.message = error.response.data?.message || error.message
          }
        } else if (error.request) {
          errorResponse.message = '网络连接失败'
        }

        return Promise.resolve({ data: errorResponse } as AxiosResponse)
      }
    )
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    try {
      const response = await this.instance.get(url, { params, ...config })
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    try {
      const response = await this.instance.post(url, data, config)
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    try {
      const response = await this.instance.put(url, data, config)
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    try {
      const response = await this.instance.delete(url, { params, ...config })
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ResponseData<T>> {
    try {
      const response = await this.instance.patch(url, data, config)
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, file: File | FormData, config?: RequestConfig): Promise<ResponseData<T>> {
    const formData = file instanceof FormData ? file : new FormData()
    if (file instanceof File) {
      formData.append('file', file)
    }

    try {
      const response = await this.instance.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        ...config
      })
      return response.data as ResponseData<T>
    } catch (error) {
      return this.handleError<T>(error)
    }
  }

  /**
   * 设置默认请求头
   */
  setHeader(key: string, value: string): void {
    this.instance.defaults.headers.common[key] = value
  }

  /**
   * 移除请求头
   */
  removeHeader(key: string): void {
    delete this.instance.defaults.headers.common[key]
  }

  /**
   * 设置 baseURL
   */
  setBaseURL(baseURL: string): void {
    this.instance.defaults.baseURL = baseURL
  }

  /**
   * 设置超时时间
   */
  setTimeout(timeout: number): void {
    this.instance.defaults.timeout = timeout
  }

  /**
   * 获取 axios 实例（用于高级用法）
   */
  getInstance(): AxiosInstance {
    return this.instance
  }

  /**
   * 错误处理
   */
  private handleError<T>(error: any): ResponseData<T> {
    return {
      success: false,
      errCode: error.response?.status || -1,
      message: error.message || '请求失败',
      data: null as T
    }
  }
}

// 创建默认实例
const request = new RequestUtil()

// 导出
export { RequestUtil }
export default request
