/**
 * API健康检查工具
 */

// API基础URL
const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api'

/**
 * 检查后端API是否可用
 */
export async function checkApiHealth(): Promise<{
  available: boolean
  message: string
  responseTime?: number
}> {
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${API_BASE}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // 设置较短的超时时间用于健康检查
      signal: AbortSignal.timeout(5000)
    })

    const responseTime = Date.now() - startTime

    if (response.ok) {
      const data = await response.json()
      return {
        available: true,
        message: data.message || 'API服务正常',
        responseTime
      }
    } else {
      return {
        available: false,
        message: `API服务异常 (HTTP ${response.status})`,
        responseTime
      }
    }
  } catch (error) {
    const responseTime = Date.now() - startTime
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return {
          available: false,
          message: 'API服务响应超时',
          responseTime
        }
      }
      
      return {
        available: false,
        message: `连接失败: ${error.message}`,
        responseTime
      }
    }
    
    return {
      available: false,
      message: '未知错误',
      responseTime
    }
  }
}

/**
 * 检查数据库连接API是否可用
 */
export async function checkDatabaseApiHealth(): Promise<{
  available: boolean
  message: string
}> {
  try {
    const response = await fetch(`${API_BASE}/database/connections`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000)
    })

    if (response.ok) {
      return {
        available: true,
        message: '数据库连接API正常'
      }
    } else {
      return {
        available: false,
        message: `数据库连接API异常 (HTTP ${response.status})`
      }
    }
  } catch (error) {
    return {
      available: false,
      message: '数据库连接API不可用'
    }
  }
}

/**
 * 获取API配置信息
 */
export function getApiConfig() {
  return {
    baseUrl: API_BASE,
    timeout: import.meta.env.VITE_API_TIMEOUT || 30000,
    backendUrl: import.meta.env.VITE_BACKEND_URL || 'http://127.0.0.1:8080',
    environment: import.meta.env.VITE_APP_ENV || 'development',
    debug: import.meta.env.VITE_DEBUG === 'true',
    mockEnabled: import.meta.env.VITE_MOCK_ENABLED === 'true'
  }
}

/**
 * 打印API配置信息到控制台
 */
export function logApiConfig() {
  const config = getApiConfig()
  console.group('🔧 API配置信息')
  console.log('基础URL:', config.baseUrl)
  console.log('后端地址:', config.backendUrl)
  console.log('超时时间:', config.timeout + 'ms')
  console.log('环境:', config.environment)
  console.log('调试模式:', config.debug)
  console.log('模拟数据:', config.mockEnabled)
  console.groupEnd()
}
