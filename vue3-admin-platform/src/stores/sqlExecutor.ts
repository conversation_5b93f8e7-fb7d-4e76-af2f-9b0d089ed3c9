import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { SqlView, DataSource } from '@/types'

/**
 * SQL执行器状态管理
 */
export const useSqlExecutorStore = defineStore('sqlExecutor', () => {
  // SQL查询内容
  const sqlQuery = ref('')
  
  // 数据源信息
  const dataSource = ref<DataSource | null>(null)
  
  // 视图信息（如果是从视图执行的）
  const currentView = ref<SqlView | null>(null)
  
  // 执行来源标识
  const executeSource = ref<'manual' | 'view' | 'datasource'>('manual')
  
  // 执行历史记录
  const executionHistory = ref<Array<{
    id: string
    sql: string
    dataSourceId: string
    dataSourceName: string
    executeTime: string
    viewName?: string
    success: boolean
    resultCount?: number
    errorMessage?: string
  }>>([])

  /**
   * 设置要执行的SQL（从视图）
   */
  const setSqlFromView = (view: SqlView, dataSourceInfo: DataSource, sql: string) => {
    sqlQuery.value = sql
    currentView.value = view
    dataSource.value = dataSourceInfo
    executeSource.value = 'view'
    
    console.log('Pinia: 设置来自视图的SQL', {
      viewName: view.name,
      dataSourceName: dataSourceInfo.name,
      sqlLength: sql.length
    })
  }

  /**
   * 设置要执行的SQL（从数据源）
   */
  const setSqlFromDataSource = (dataSourceInfo: DataSource, sql: string = '') => {
    sqlQuery.value = sql
    currentView.value = null
    dataSource.value = dataSourceInfo
    executeSource.value = 'datasource'
    
    console.log('Pinia: 设置来自数据源的SQL', {
      dataSourceName: dataSourceInfo.name,
      sqlLength: sql.length
    })
  }

  /**
   * 手动设置SQL
   */
  const setSqlManually = (sql: string) => {
    sqlQuery.value = sql
    executeSource.value = 'manual'
    
    console.log('Pinia: 手动设置SQL', {
      sqlLength: sql.length
    })
  }

  /**
   * 清空SQL内容
   */
  const clearSql = () => {
    sqlQuery.value = ''
    currentView.value = null
    executeSource.value = 'manual'
    
    console.log('Pinia: 清空SQL内容')
  }

  /**
   * 添加执行历史记录
   */
  const addExecutionHistory = (record: {
    sql: string
    dataSourceId: string
    dataSourceName: string
    viewName?: string
    success: boolean
    resultCount?: number
    errorMessage?: string
  }) => {
    const historyRecord = {
      id: Date.now().toString(),
      executeTime: new Date().toISOString(),
      ...record
    }
    
    executionHistory.value.unshift(historyRecord)
    
    // 只保留最近50条记录
    if (executionHistory.value.length > 50) {
      executionHistory.value = executionHistory.value.slice(0, 50)
    }
    
    console.log('Pinia: 添加执行历史记录', historyRecord)
  }

  /**
   * 清空执行历史
   */
  const clearExecutionHistory = () => {
    executionHistory.value = []
    console.log('Pinia: 清空执行历史')
  }

  /**
   * 获取当前执行上下文信息
   */
  const getExecutionContext = () => {
    return {
      sql: sqlQuery.value,
      dataSource: dataSource.value,
      currentView: currentView.value,
      executeSource: executeSource.value,
      hasContent: !!sqlQuery.value.trim()
    }
  }

  /**
   * 重置所有状态
   */
  const reset = () => {
    sqlQuery.value = ''
    dataSource.value = null
    currentView.value = null
    executeSource.value = 'manual'
    
    console.log('Pinia: 重置所有状态')
  }

  return {
    // 状态
    sqlQuery,
    dataSource,
    currentView,
    executeSource,
    executionHistory,
    
    // 方法
    setSqlFromView,
    setSqlFromDataSource,
    setSqlManually,
    clearSql,
    addExecutionHistory,
    clearExecutionHistory,
    getExecutionContext,
    reset
  }
})
