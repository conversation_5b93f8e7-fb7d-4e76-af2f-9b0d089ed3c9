import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ThemeMode } from '@/types'

/**
 * 主题状态管理
 */
export const useThemeStore = defineStore('theme', () => {
  // 当前主题模式
  const mode = ref<ThemeMode>('light')
  
  // 侧边栏是否折叠
  const sidebarCollapsed = ref(false)
  
  // 是否为移动端
  const isMobile = ref(false)

  // 计算属性：是否为深色主题
  const isDark = computed(() => {
    if (mode.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return mode.value === 'dark'
  })

  /**
   * 切换主题模式
   */
  const toggleTheme = () => {
    mode.value = mode.value === 'light' ? 'dark' : 'light'
    localStorage.setItem('theme-mode', mode.value)
  }

  /**
   * 设置主题模式
   */
  const setTheme = (newMode: ThemeMode) => {
    mode.value = newMode
    localStorage.setItem('theme-mode', newMode)
  }

  /**
   * 切换侧边栏折叠状态
   */
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    localStorage.setItem('sidebar-collapsed', String(sidebarCollapsed.value))
  }

  /**
   * 设置移动端状态
   */
  const setMobile = (mobile: boolean) => {
    isMobile.value = mobile
    // 移动端自动折叠侧边栏
    if (mobile) {
      sidebarCollapsed.value = true
    }
  }

  /**
   * 初始化主题设置
   */
  const initTheme = () => {
    // 从本地存储恢复主题设置
    const savedTheme = localStorage.getItem('theme-mode') as ThemeMode
    if (savedTheme) {
      mode.value = savedTheme
    }

    const savedSidebar = localStorage.getItem('sidebar-collapsed')
    if (savedSidebar) {
      sidebarCollapsed.value = savedSidebar === 'true'
    }

    // 监听系统主题变化
    if (mode.value === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', () => {
        // 触发响应式更新
        mode.value = 'auto'
      })
    }
  }

  return {
    mode,
    sidebarCollapsed,
    isMobile,
    isDark,
    toggleTheme,
    setTheme,
    toggleSidebar,
    setMobile,
    initTheme
  }
})
