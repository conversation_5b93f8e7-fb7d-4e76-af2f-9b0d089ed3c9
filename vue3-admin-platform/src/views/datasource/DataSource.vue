<template>
  <div class="datasource">
    <n-space vertical size="large">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>数据源管理</h1>
        <p>管理和配置系统数据源</p>
      </div>

      <!-- 操作栏 -->
      <n-card size="small">
        <n-space justify="space-between">
          <n-space>
            <n-button type="primary" @click="loadDataSources">
              <template #icon>
                <n-icon>
                  <RefreshIcon />
                </n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
          <n-space>
            <n-input
              v-model:value="searchKeyword"
              placeholder="搜索数据源..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <n-icon>
                  <SearchIcon />
                </n-icon>
              </template>
            </n-input>
          </n-space>
        </n-space>
      </n-card>

      <!-- 数据源列表 -->
      <n-card title="数据源列表" size="small">
        <n-spin :show="loading">
          <n-data-table
            :columns="columns"
            :data="filteredDataSources"
            :pagination="pagination"
            :loading="loading"
            striped
            :row-key="(row: DataSource) => row.id"
          />
        </n-spin>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  NSpace,
  NCard,
  NButton,
  NIcon,
  NInput,
  NDataTable,
  NSpin,
  NTag,
  type DataTableColumns
} from 'naive-ui'
import {
  RefreshOutline as RefreshIcon,
  SearchOutline as SearchIcon,
  ServerOutline as DatabaseIcon,
  PlayOutline as PlayIcon,
  EyeOutline as EyeIcon
} from '@vicons/ionicons5'
import { getDataSources } from '@/api'
import { debugConfig } from '@/api/config'
import { useSqlExecutorStore } from '@/stores/sqlExecutor'
import type { DataSource } from '@/types'

/**
 * 数据源管理页面
 */

// 路由
const router = useRouter()

// Pinia store
const sqlExecutorStore = useSqlExecutorStore()

// 响应式数据
const loading = ref(false)
const dataSources = ref<DataSource[]>([])
const searchKeyword = ref('')

// 分页配置
const pagination = {
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

// 表格列配置
const columns: DataTableColumns<DataSource> = [
  {
    title: '名称',
    key: 'name',
    width: 200,
    render(row) {
      return h('div', { class: 'flex items-center' }, [
        h(NIcon, { size: 18, color: '#18a058', class: 'mr-2' }, {
          default: () => h(DatabaseIcon)
        }),
        h('span', { class: 'font-medium' }, row.name)
      ])
    }
  },
  {
    title: '类型',
    key: 'type',
    width: 100,
    render(row) {
      return h(NTag, { 
        type: 'info',
        size: 'small'
      }, { default: () => row.type })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.status === 1 ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => row.status === 1 ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180,
    render(row) {
      return new Date(row.createTime).toLocaleString('zh-CN')
    }
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 180,
    render(row) {
      return new Date(row.updateTime).toLocaleString('zh-CN')
    }
  },
  {
    title: '创建人',
    key: 'createBy',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    render(row) {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, {
            type: 'info',
            size: 'small',
            onClick: () => handleViewDetail(row)
          }, {
            default: () => [
              h(NIcon, { size: 14, class: 'mr-1' }, {
                default: () => h(EyeIcon)
              }),
              '详情'
            ]
          }),
          h(NButton, {
            type: 'primary',
            size: 'small',
            onClick: () => handleExecuteSQL(row)
          }, {
            default: () => [
              h(NIcon, { size: 14, class: 'mr-1' }, {
                default: () => h(PlayIcon)
              }),
              'SQL'
            ]
          })
        ]
      })
    }
  }
]

// 计算属性：过滤后的数据源
const filteredDataSources = computed(() => {
  if (!searchKeyword.value) {
    return dataSources.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return dataSources.value.filter(item => 
    item.name.toLowerCase().includes(keyword) ||
    item.type.toLowerCase().includes(keyword)
  )
})

/**
 * 加载数据源列表
 */
const loadDataSources = async () => {
  loading.value = true

  // 调试：打印当前配置信息
  debugConfig()

  try {
    const response = await getDataSources()
    if (response.success) {
      dataSources.value = response.data || []
      console.log('数据源列表加载成功:', dataSources.value.length, '条')
    } else {
      console.error('加载数据源失败:', response.message)
      dataSources.value = []
    }
  } catch (error) {
    console.error('加载数据源异常:', error)
    dataSources.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
  console.log('搜索关键词:', searchKeyword.value)
}

/**
 * 查看数据源详情
 */
const handleViewDetail = (dataSource: DataSource) => {
  console.log('查看数据源详情:', dataSource.name, 'ID:', dataSource.id)
  // 跳转到数据源详情页面
  router.push({
    name: 'datasource-detail',
    params: { id: dataSource.id }
  })
}

/**
 * 处理执行SQL操作
 */
const handleExecuteSQL = (dataSource: DataSource) => {
  console.log('选择数据源执行SQL:', dataSource.name, 'ID:', dataSource.id)

  // 使用Pinia store设置数据源信息（清空SQL内容）
  sqlExecutorStore.setSqlFromDataSource(dataSource, '')

  // 跳转到SQL执行页面，带上数据源ID
  router.push({
    name: 'sql-executor',
    params: { id: dataSource.id },
    query: { name: dataSource.name }
  })
}

// 页面加载时获取数据
onMounted(() => {
  loadDataSources()
})
</script>

<style scoped>
.datasource {
  padding: 0;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}
</style>
