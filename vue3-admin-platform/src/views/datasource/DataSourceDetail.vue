<template>
  <div class="datasource-detail">
    <n-space vertical size="large">
      <!-- 页面标题 -->
      <div class="page-header">
        <n-space align="center">
          <n-button text @click="goBack">
            <template #icon>
              <n-icon>
                <ArrowBackIcon />
              </n-icon>
            </template>
          </n-button>
          <div>
            <h1>{{ dataSource?.name || '数据源详情' }}</h1>
            <p>数据源类型: {{ dataSource?.type }} | 状态: {{ dataSource?.status === 1 ? '启用' : '禁用' }}</p>
          </div>
        </n-space>
      </div>

      <!-- 数据源信息卡片 -->
      <n-card title="数据源信息" size="small">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="数据源名称">
            {{ dataSource?.name || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="数据源类型">
            <n-tag type="info" size="small">{{ dataSource?.type || '-' }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="dataSource?.status === 1 ? 'success' : 'error'" size="small">
              {{ dataSource?.status === 1 ? '启用' : '禁用' }}
            </n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ dataSource?.createTime ? new Date(dataSource.createTime).toLocaleString('zh-CN') : '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="创建人">
            {{ dataSource?.createBy || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="最后更新">
            {{ dataSource?.updateTime ? new Date(dataSource.updateTime).toLocaleString('zh-CN') : '-' }}
          </n-descriptions-item>
        </n-descriptions>
      </n-card>

      <!-- 操作按钮 -->
      <n-card size="small">
        <n-space>
          <n-button type="primary" @click="executeSQL">
            <template #icon>
              <n-icon>
                <PlayIcon />
              </n-icon>
            </template>
            执行SQL
          </n-button>
          <n-button @click="refreshViews">
            <template #icon>
              <n-icon>
                <RefreshIcon />
              </n-icon>
            </template>
            刷新视图
          </n-button>
        </n-space>
      </n-card>

      <!-- 该数据源的视图列表 -->
      <n-card title="关联视图" size="small">
        <template #header-extra>
          <n-tag type="info" size="small">
            共 {{ relatedViews.length }} 个视图
          </n-tag>
        </template>
        
        <n-spin :show="viewsLoading">
          <div v-if="relatedViews.length > 0">
            <n-data-table
              :columns="viewColumns"
              :data="relatedViews"
              :pagination="viewPagination"
              striped
              size="small"
              :row-key="(row: SqlView) => row.id"
            />
          </div>
          <n-empty v-else description="该数据源暂无关联视图" />
        </n-spin>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NSpace,
  NCard,
  NButton,
  NIcon,
  NDescriptions,
  NDescriptionsItem,
  NTag,
  NDataTable,
  NSpin,
  NEmpty,
  NText,
  type DataTableColumns
} from 'naive-ui'
import {
  ArrowBackOutline as ArrowBackIcon,
  PlayOutline as PlayIcon,
  RefreshOutline as RefreshIcon,
  EyeOutline as ViewIcon,
  DocumentTextOutline as DocumentIcon
} from '@vicons/ionicons5'
import { getDataSources, getViews, getViewById } from '@/api'
import { useSqlExecutorStore } from '@/stores/sqlExecutor'
import type { DataSource, SqlView } from '@/types'

/**
 * 数据源详情页面
 */

// 路由
const route = useRoute()
const router = useRouter()

// Pinia store
const sqlExecutorStore = useSqlExecutorStore()

// 响应式数据
const dataSource = ref<DataSource | null>(null)
const relatedViews = ref<SqlView[]>([])
const viewsLoading = ref(false)

// 分页配置
const viewPagination = {
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

// 视图表格列配置
const viewColumns: DataTableColumns<SqlView> = [
  {
    title: '视图名称',
    key: 'name',
    width: 300,
    render(row) {
      return h('div', { class: 'flex items-center' }, [
        h(NIcon, { size: 18, color: '#18a058', class: 'mr-2' }, {
          default: () => h(DocumentIcon)
        }),
        h('span', { class: 'font-medium' }, row.name || '未命名视图')
      ])
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: (row.status === 1 || row.status === undefined) ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => (row.status === 1 || row.status === undefined) ? '启用' : '禁用'
      })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render(row) {
      return h(NButton, {
        type: 'primary',
        size: 'small',
        onClick: () => handleExecuteView(row)
      }, {
        default: () => [
          h(NIcon, { size: 14, class: 'mr-1' }, {
            default: () => h(PlayIcon)
          }),
          '执行'
        ]
      })
    }
  }
]

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 执行SQL
 */
const executeSQL = () => {
  if (dataSource.value) {
    router.push({
      name: 'sql-executor',
      params: { id: dataSource.value.id },
      query: { name: dataSource.value.name }
    })
  }
}

/**
 * 加载数据源详情
 */
const loadDataSource = async () => {
  const dataSourceId = route.params.id as string
  if (!dataSourceId) return

  try {
    const response = await getDataSources()
    if (response.success) {
      const foundDataSource = response.data?.find(ds => ds.id === dataSourceId)
      if (foundDataSource) {
        dataSource.value = foundDataSource
        console.log('数据源详情加载成功:', foundDataSource.name)
      } else {
        console.error('未找到指定的数据源')
      }
    }
  } catch (error) {
    console.error('加载数据源详情异常:', error)
  }
}

/**
 * 加载关联视图
 */
const loadRelatedViews = async () => {
  const dataSourceId = route.params.id as string
  if (!dataSourceId) return

  viewsLoading.value = true
  try {
    const response = await getViews()
    if (response.success) {
      // 过滤出属于当前数据源的视图
      relatedViews.value = (response.data || []).filter(view => view.sourceId === dataSourceId)
      console.log('关联视图加载成功:', relatedViews.value.length, '条')
    } else {
      console.error('加载视图失败:', response.message)
      relatedViews.value = []
    }
  } catch (error) {
    console.error('加载视图异常:', error)
    relatedViews.value = []
  } finally {
    viewsLoading.value = false
  }
}

/**
 * 刷新视图
 */
const refreshViews = () => {
  loadRelatedViews()
}

/**
 * 执行视图
 */
const handleExecuteView = async (view: SqlView) => {
  if (!dataSource.value || !view.id) return

  try {
    console.log('获取视图详情:', view.name, 'ID:', view.id)

    // 调用API获取视图详情（包含完整的SQL）
    const response = await getViewById(view.id)

    if (response.success && response.data) {
      const viewDetail = response.data
      console.log('视图详情获取成功:', viewDetail)
      console.log('视图SQL内容(sql字段):', viewDetail.sql)
      console.log('视图SQL内容(script字段):', viewDetail.script)
      console.log('视图SQL长度:', (viewDetail.script || viewDetail.sql)?.length || 0)

      // 检查SQL内容（优先使用script字段，然后是sql字段）
      const sqlContent = viewDetail.script || viewDetail.sql || view.script || view.sql || ''
      console.log('最终使用的SQL:', sqlContent)

      // 使用Pinia store设置SQL内容
      sqlExecutorStore.setSqlFromView(viewDetail, dataSource.value, sqlContent)

      // 跳转到SQL执行页面（不再通过URL传递SQL）
      router.push({
        name: 'sql-executor',
        params: { id: dataSource.value.id },
        query: {
          name: dataSource.value.name,
          viewName: viewDetail.name || view.name
        }
      })
    } else {
      console.error('获取视图详情失败:', response.message)
      // 如果获取详情失败，使用列表中的基本信息
      const fallbackSql = view.script || view.sql || ''
      sqlExecutorStore.setSqlFromView(view, dataSource.value, fallbackSql)

      router.push({
        name: 'sql-executor',
        params: { id: dataSource.value.id },
        query: {
          name: dataSource.value.name,
          viewName: view.name
        }
      })
    }
  } catch (error) {
    console.error('获取视图详情异常:', error)
    // 异常情况下使用基本信息
    const fallbackSql = view.script || view.sql || ''
    sqlExecutorStore.setSqlFromView(view, dataSource.value, fallbackSql)

    router.push({
      name: 'sql-executor',
      params: { id: dataSource.value.id },
      query: {
        name: dataSource.value.name,
        viewName: view.name
      }
    })
  }
}

// 页面初始化
onMounted(async () => {
  await loadDataSource()
  await loadRelatedViews()
})
</script>

<style scoped>
.datasource-detail {
  padding: 0;
}

.page-header h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 8px 12px;
}
</style>
