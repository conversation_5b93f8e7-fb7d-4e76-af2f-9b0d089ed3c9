<template>
  <div class="dashboard">
    <n-space vertical size="large">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>仪表板</h1>
        <p>欢迎使用Vue 3后台管理平台</p>
      </div>

      <!-- 数据概览卡片 -->
      <n-grid :cols="4" :x-gap="16" :y-gap="16" responsive="screen">
        <n-grid-item v-for="card in overviewCards" :key="card.title">
          <n-card :title="card.title" size="small">
            <template #header-extra>
              <n-icon size="24" :color="card.color">
                <component :is="card.icon" />
              </n-icon>
            </template>
            <div class="card-content">
              <div class="card-value">{{ card.value }}</div>
              <div class="card-change" :class="card.changeType">
                {{ card.change }}
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 图表展示区域 -->
      <n-grid :cols="2" :x-gap="16" :y-gap="16" responsive="screen">
        <n-grid-item>
          <n-card title="访问趋势" size="small">
            <div class="chart-placeholder">
              <n-empty description="图表组件待实现">
                <template #icon>
                  <n-icon>
                    <StatsChartIcon />
                  </n-icon>
                </template>
              </n-empty>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card title="用户分布" size="small">
            <div class="chart-placeholder">
              <n-empty description="图表组件待实现">
                <template #icon>
                  <n-icon>
                    <PieChartIcon />
                  </n-icon>
                </template>
              </n-empty>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>

      <!-- 快捷操作入口 -->
      <n-card title="快捷操作" size="small">
        <n-space>
          <n-button
            v-for="action in quickActions"
            :key="action.key"
            :type="action.type as any"
            @click="handleQuickAction(action.key)"
          >
            <template #icon>
              <n-icon>
                <component :is="action.icon" />
              </n-icon>
            </template>
            {{ action.label }}
          </n-button>
        </n-space>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { useRouter } from 'vue-router'
import {
  NSpace,
  NGrid,
  NGridItem,
  NCard,
  NIcon,
  NEmpty,
  NButton
} from 'naive-ui'
import {
  PersonOutline as PersonIcon,
  DocumentTextOutline as DocumentIcon,
  StatsChartOutline as StatsChartIcon,
  PieChartSharp as PieChartIcon,
  AddOutline as AddIcon,
  SettingsOutline as SettingsIcon,
  RefreshOutline as RefreshIcon
} from '@vicons/ionicons5'

/**
 * 仪表板页面
 * 数据概览卡片、图表展示区域、快捷操作入口
 */

const router = useRouter()

// 数据概览卡片
const overviewCards = [
  {
    title: '总用户数',
    value: '1,234',
    change: '+12%',
    changeType: 'positive',
    color: '#18a058',
    icon: PersonIcon
  },
  {
    title: '今日访问',
    value: '856',
    change: '+8%',
    changeType: 'positive',
    color: '#2080f0',
    icon: StatsChartIcon
  },
  {
    title: '文档数量',
    value: '342',
    change: '+5%',
    changeType: 'positive',
    color: '#f0a020',
    icon: DocumentIcon
  },
  {
    title: '系统状态',
    value: '正常',
    change: '99.9%',
    changeType: 'positive',
    color: '#18a058',
    icon: SettingsIcon
  }
]

// 快捷操作
const quickActions = [
  {
    key: 'add-user',
    label: '添加用户',
    type: 'primary',
    icon: AddIcon
  },
  {
    key: 'system-settings',
    label: '系统设置',
    type: 'default',
    icon: SettingsIcon
  },
  {
    key: 'refresh-data',
    label: '刷新数据',
    type: 'default',
    icon: RefreshIcon
  }
]

/**
 * 处理快捷操作
 */
const handleQuickAction = (key: string) => {
  switch (key) {
    case 'add-user':
      router.push('/user/list')
      break
    case 'system-settings':
      router.push('/system/settings')
      break
    case 'refresh-data':
      // 刷新数据逻辑
      console.log('刷新数据')
      break
  }
}
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.card-change {
  font-size: 14px;
  font-weight: 500;
}

.card-change.positive {
  color: #18a058;
}

.card-change.negative {
  color: #d03050;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
