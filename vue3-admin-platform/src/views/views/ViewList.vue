<template>
  <div class="view-list">
    <n-space vertical size="large">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>视图管理</h1>
        <p>管理和查看已保存的SQL视图，按数据源分组显示</p>
      </div>

      <!-- 操作栏 -->
      <n-card size="small">
        <n-space justify="space-between">
          <n-space>
            <n-button type="primary" @click="loadAllData">
              <template #icon>
                <n-icon>
                  <RefreshIcon />
                </n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
          <n-space>
            <n-input
              v-model:value="searchKeyword"
              placeholder="搜索视图..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <n-icon>
                  <SearchIcon />
                </n-icon>
              </template>
            </n-input>
          </n-space>
        </n-space>
      </n-card>

      <!-- 视图列表 -->
      <n-card title="视图列表" size="small">
        <n-spin :show="loading">
          <n-data-table
            :columns="columns"
            :data="filteredViews"
            :pagination="pagination"
            :loading="loading"
            striped
            :row-key="(row: SqlView) => row.id"
          />
        </n-spin>
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  NSpace,
  NCard,
  NButton,
  NIcon,
  NInput,
  NDataTable,
  NSpin,
  NTag,
  NText,
  type DataTableColumns
} from 'naive-ui'
import {
  RefreshOutline as RefreshIcon,
  SearchOutline as SearchIcon,
  EyeOutline as ViewIcon,
  PlayOutline as PlayIcon,
  DocumentTextOutline as DocumentIcon
} from '@vicons/ionicons5'
import { getViews, getDataSources, getViewById } from '@/api'
import { useSqlExecutorStore } from '@/stores/sqlExecutor'
import type { SqlView, DataSource } from '@/types'

/**
 * 视图列表页面
 */

// 路由
const router = useRouter()

// Pinia store
const sqlExecutorStore = useSqlExecutorStore()

// 响应式数据
const loading = ref(false)
const views = ref<SqlView[]>([])
const dataSources = ref<DataSource[]>([])
const searchKeyword = ref('')

// 数据源分组的视图
interface GroupedViews {
  dataSource: DataSource
  views: SqlView[]
}

const groupedViews = ref<GroupedViews[]>([])

// 分页配置
const pagination = {
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  showQuickJumper: true
}

// 表格列配置
const columns: DataTableColumns<SqlView> = [
  {
    title: '视图名称',
    key: 'name',
    width: 300,
    render(row) {
      return h('div', { class: 'flex items-center' }, [
        h(NIcon, { size: 18, color: '#18a058', class: 'mr-2' }, {
          default: () => h(DocumentIcon)
        }),
        h('span', { class: 'font-medium' }, row.name || '未命名视图')
      ])
    }
  },
  {
    title: '数据源',
    key: 'sourceName',
    width: 200,
    render(row) {
      const sourceName = getDataSourceName(row.sourceId)
      return h(NTag, {
        type: 'info',
        size: 'small'
      }, {
        default: () => sourceName || '未知数据源'
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      return h(NTag, {
        type: (row.status === 1 || row.status === undefined) ? 'success' : 'error',
        size: 'small'
      }, {
        default: () => (row.status === 1 || row.status === undefined) ? '启用' : '禁用'
      })
    }
  },
  {
    title: '创建人',
    key: 'createBy',
    width: 120,
    render(row) {
      return row.createBy || '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render(row) {
      return h(NButton, {
        type: 'primary',
        size: 'small',
        onClick: () => handleExecuteView(row)
      }, {
        default: () => [
          h(NIcon, { size: 14, class: 'mr-1' }, {
            default: () => h(PlayIcon)
          }),
          '执行'
        ]
      })
    }
  }
]

/**
 * 根据数据源ID获取数据源名称
 */
const getDataSourceName = (sourceId?: string): string => {
  if (!sourceId) return '未知数据源'
  const dataSource = dataSources.value.find(ds => ds.id === sourceId)
  return dataSource?.name || '未知数据源'
}

/**
 * 按数据源分组视图
 */
const groupViewsByDataSource = () => {
  const groups: GroupedViews[] = []

  // 为每个数据源创建分组
  dataSources.value.forEach(dataSource => {
    const sourceViews = views.value.filter(view => view.sourceId === dataSource.id)
    if (sourceViews.length > 0) {
      groups.push({
        dataSource,
        views: sourceViews
      })
    }
  })

  // 处理没有关联数据源的视图
  const orphanViews = views.value.filter(view =>
    !view.sourceId || !dataSources.value.find(ds => ds.id === view.sourceId)
  )

  if (orphanViews.length > 0) {
    groups.push({
      dataSource: {
        id: 'unknown',
        name: '未知数据源',
        type: 'UNKNOWN',
        config: '{}',
        orgId: '',
        isFolder: false,
        status: 0,
        createBy: '',
        createTime: '',
        updateBy: '',
        updateTime: ''
      },
      views: orphanViews
    })
  }

  groupedViews.value = groups
}

// 计算属性：过滤后的视图
const filteredViews = computed(() => {
  if (!searchKeyword.value) {
    return views.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return views.value.filter(item =>
    (item.name || '').toLowerCase().includes(keyword) ||
    getDataSourceName(item.sourceId).toLowerCase().includes(keyword) ||
    (item.createBy || '').toLowerCase().includes(keyword)
  )
})

/**
 * 加载数据源列表
 */
const loadDataSources = async () => {
  try {
    const response = await getDataSources()
    if (response.success) {
      dataSources.value = response.data || []
      console.log('数据源列表加载成功:', dataSources.value.length, '条')
    } else {
      console.error('加载数据源失败:', response.message)
      dataSources.value = []
    }
  } catch (error) {
    console.error('加载数据源异常:', error)
    dataSources.value = []
  }
}

/**
 * 加载视图列表
 */
const loadViews = async () => {
  try {
    const response = await getViews()
    if (response.success) {
      views.value = response.data || []
      console.log('视图列表加载成功:', views.value.length, '条')
    } else {
      console.error('加载视图失败:', response.message)
      views.value = []
    }
  } catch (error) {
    console.error('加载视图异常:', error)
    views.value = []
  }
}

/**
 * 加载所有数据
 */
const loadAllData = async () => {
  loading.value = true
  try {
    // 并行加载数据源和视图
    await Promise.all([
      loadDataSources(),
      loadViews()
    ])

    // 加载完成后进行分组
    groupViewsByDataSource()
  } finally {
    loading.value = false
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
}

/**
 * 执行视图
 */
const handleExecuteView = async (view: SqlView) => {
  console.log('执行视图:', view.name, 'ID:', view.id)

  if (!view.id || !view.sourceId) {
    console.warn('视图缺少必要信息:', view)
    return
  }

  try {
    console.log('获取视图详情:', view.name, 'ID:', view.id)

    // 调用API获取视图详情（包含完整的SQL）
    const response = await getViewById(view.id)

    if (response.success && response.data) {
      const viewDetail = response.data
      console.log('视图详情获取成功:', viewDetail)
      console.log('视图SQL内容(sql字段):', viewDetail.sql)
      console.log('视图SQL内容(script字段):', viewDetail.script)
      console.log('视图SQL长度:', (viewDetail.script || viewDetail.sql)?.length || 0)

      // 检查SQL内容（优先使用script字段，然后是sql字段）
      const sqlContent = viewDetail.script || viewDetail.sql || view.script || view.sql || ''
      console.log('最终使用的SQL:', sqlContent)

      // 获取数据源信息
      const dataSourceInfo = dataSources.value.find(ds => ds.id === view.sourceId)
      if (!dataSourceInfo) {
        console.error('未找到数据源信息:', view.sourceId)
        return
      }

      // 使用Pinia store设置SQL内容
      sqlExecutorStore.setSqlFromView(viewDetail, dataSourceInfo, sqlContent)

      // 跳转到SQL执行页面（不再通过URL传递SQL）
      router.push({
        name: 'sql-executor',
        params: { id: view.sourceId },
        query: {
          name: getDataSourceName(view.sourceId),
          viewName: viewDetail.name || view.name
        }
      })
    } else {
      console.error('获取视图详情失败:', response.message)
      // 如果获取详情失败，使用列表中的基本信息
      const dataSourceInfo = dataSources.value.find(ds => ds.id === view.sourceId)
      if (dataSourceInfo) {
        const fallbackSql = view.script || view.sql || ''
        sqlExecutorStore.setSqlFromView(view, dataSourceInfo, fallbackSql)
      }

      router.push({
        name: 'sql-executor',
        params: { id: view.sourceId },
        query: {
          name: getDataSourceName(view.sourceId),
          viewName: view.name
        }
      })
    }
  } catch (error) {
    console.error('获取视图详情异常:', error)
    // 异常情况下使用基本信息
    const dataSourceInfo = dataSources.value.find(ds => ds.id === view.sourceId)
    if (dataSourceInfo) {
      const fallbackSql = view.script || view.sql || ''
      sqlExecutorStore.setSqlFromView(view, dataSourceInfo, fallbackSql)
    }

    router.push({
      name: 'sql-executor',
      params: { id: view.sourceId },
      query: {
        name: getDataSourceName(view.sourceId),
        viewName: view.name
      }
    })
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadAllData()
})
</script>

<style scoped>
.view-list {
  padding: 0;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-data-table-td) {
  padding: 12px 16px;
}
</style>
