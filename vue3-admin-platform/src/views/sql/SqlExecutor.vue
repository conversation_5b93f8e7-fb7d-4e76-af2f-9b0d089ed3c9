<template>
  <div class="sql-executor">
    <n-space vertical size="large">
      <!-- 页面标题 -->
      <div class="page-header">
        <n-space align="center">
          <n-button text @click="goBack">
            <template #icon>
              <n-icon>
                <ArrowBackIcon />
              </n-icon>
            </template>
          </n-button>
          <div>
            <h1>SQL执行器</h1>
            <p>数据源: {{ dataSourceName }} (ID: {{ dataSourceId }})</p>
          </div>
        </n-space>
      </div>

      <!-- SQL编辑器 -->
      <n-card title="SQL编辑器" size="small">
        <template #header-extra>
          <n-space>
            <n-button type="primary" @click="executeSQLQuery" :loading="executing">
              <template #icon>
                <n-icon>
                  <PlayIcon />
                </n-icon>
              </template>
              执行SQL
            </n-button>
            <n-button @click="clearSQL">
              <template #icon>
                <n-icon>
                  <TrashIcon />
                </n-icon>
              </template>
              清空
            </n-button>
          </n-space>
        </template>
        
        <n-input
          v-model:value="sqlQuery"
          type="textarea"
          placeholder="请输入SQL查询语句..."
          :rows="8"
          :autosize="{ minRows: 8, maxRows: 15 }"
          show-count
        />
      </n-card>

      <!-- 执行结果 -->
      <n-card title="执行结果" size="small" v-if="hasResult">
        <template #header-extra>
          <n-space>
            <n-tag v-if="executionTime" type="info" size="small">
              执行时间: {{ executionTime }}ms
            </n-tag>
            <n-tag v-if="resultCount !== null" type="success" size="small">
              结果数量: {{ resultCount }}条
            </n-tag>
          </n-space>
        </template>

        <!-- 成功结果 -->
        <div v-if="queryResult && queryResult.success">
          <n-data-table
            :columns="resultColumns"
            :data="tableData"
            :pagination="resultPagination"
            :max-height="400"
            striped
            size="small"
            :row-key="(row: TableRow) => row.id"
          />
        </div>

        <!-- 错误信息 -->
        <div v-else-if="queryResult && !queryResult.success">
          <n-alert type="error" title="SQL执行失败">
            {{ queryResult.message }}
          </n-alert>
        </div>
      </n-card>

      <!-- SQL历史记录 -->
      <n-card title="执行历史" size="small">
        <n-list v-if="sqlHistory.length > 0">
          <n-list-item v-for="(item, index) in sqlHistory" :key="index">
            <template #prefix>
              <n-icon :color="item.success ? '#18a058' : '#d03050'">
                <CheckmarkIcon v-if="item.success" />
                <CloseIcon v-else />
              </n-icon>
            </template>
            <n-thing>
              <template #header>
                <n-text code>{{ item.sql.substring(0, 100) }}{{ item.sql.length > 100 ? '...' : '' }}</n-text>
              </template>
              <template #description>
                <n-space>
                  <n-text depth="3">{{ item.timestamp }}</n-text>
                  <n-text depth="3">耗时: {{ item.executionTime }}ms</n-text>
                  <n-text depth="3" v-if="item.success">结果: {{ item.rowCount }}条</n-text>
                </n-space>
              </template>
            </n-thing>
            <template #suffix>
              <n-button text size="small" @click="rerunSQL(item.sql)">
                重新执行
              </n-button>
            </template>
          </n-list-item>
        </n-list>
        <n-empty v-else description="暂无执行历史" />
      </n-card>
    </n-space>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NSpace,
  NCard,
  NButton,
  NIcon,
  NInput,
  NDataTable,
  NTag,
  NAlert,
  NList,
  NListItem,
  NThing,
  NText,
  NEmpty,
  type DataTableColumns
} from 'naive-ui'
import {
  ArrowBackOutline as ArrowBackIcon,
  PlayOutline as PlayIcon,
  TrashOutline as TrashIcon,
  CheckmarkOutline as CheckmarkIcon,
  CloseOutline as CloseIcon
} from '@vicons/ionicons5'
import { executeSQL } from '@/api'
import { useSqlExecutorStore } from '@/stores/sqlExecutor'
import type { SqlExecuteRequest, SqlExecuteResultData, TableRow, TableColumn } from '@/types'

/**
 * SQL执行器页面
 */

// 路由
const route = useRoute()
const router = useRouter()

// Pinia store
const sqlExecutorStore = useSqlExecutorStore()

// 数据源信息
const dataSourceId = ref<string>('')
const dataSourceName = ref<string>('')

// SQL相关
const sqlQuery = ref('')
const executing = ref(false)
const queryResult = ref<any>(null)
const executionTime = ref<number | null>(null)
const resultCount = ref<number | null>(null)

// 历史记录
interface SqlHistoryItem {
  sql: string
  success: boolean
  timestamp: string
  executionTime: number
  rowCount?: number
  message?: string
}

const sqlHistory = ref<SqlHistoryItem[]>([])

// 计算属性
const hasResult = computed(() => queryResult.value !== null)

// 转换后的表格数据
const tableData = ref<TableRow[]>([])
const tableColumns = ref<TableColumn[]>([])

const resultColumns = computed<DataTableColumns>(() => {
  return tableColumns.value.map((col) => ({
    title: col.title,
    key: col.key,
    width: 150,
    ellipsis: {
      tooltip: true
    }
  }))
})

/**
 * 转换SQL结果数据为表格数据
 */
const transformSqlResultToTable = (sqlResult: SqlExecuteResultData): { columns: TableColumn[], data: TableRow[] } => {
  // 转换列定义
  const columns: TableColumn[] = sqlResult.columns.map((col, index) => ({
    title: col.name[0] || `Column_${index}`, // 使用name数组的第一个元素作为标题
    key: `col_${index}`,
    dataIndex: `col_${index}`,
    type: col.type,
    width: 150
  }))

  // 转换行数据
  const data: TableRow[] = sqlResult.rows.map((row, rowIndex) => {
    const tableRow: TableRow = { id: rowIndex }
    row.forEach((cellValue, colIndex) => {
      tableRow[`col_${colIndex}`] = cellValue
    })
    return tableRow
  })

  return { columns, data }
}

const resultPagination = {
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 执行SQL
 */
const executeSQLQuery = async () => {
  if (!sqlQuery.value.trim()) {
    return
  }

  executing.value = true
  const startTime = Date.now()

  try {
    // 构建请求参数
    const requestParams: SqlExecuteRequest = {
      script: sqlQuery.value,
      sourceId: dataSourceId.value,
      size: 1000,
      scriptType: 'SQL',
      columns: '',
      variables: []
    }

    // 调用真实API
    const response = await executeSQL(requestParams)

    if (response.success && response.data) {
      // 转换数据格式
      const { columns, data } = transformSqlResultToTable(response.data)

      // 更新表格数据
      tableColumns.value = columns
      tableData.value = data

      // 成功结果
      const result = {
        success: true,
        data: data,
        columns: columns,
        rowCount: response.data.rows.length,
        executionTime: Date.now() - startTime
      }

      queryResult.value = result
      executionTime.value = result.executionTime
      resultCount.value = result.rowCount

      // 添加到历史记录
      addToHistory(sqlQuery.value, true, result.executionTime, result.rowCount)

    } else {
      // API返回失败
      const errorResult = {
        success: false,
        message: response.message || 'SQL执行失败'
      }

      queryResult.value = errorResult
      executionTime.value = Date.now() - startTime
      resultCount.value = null

      // 添加到历史记录
      addToHistory(sqlQuery.value, false, Date.now() - startTime, 0, errorResult.message)
    }

  } catch (error) {
    // 网络错误或其他异常
    const errorResult = {
      success: false,
      message: error instanceof Error ? error.message : 'SQL执行失败'
    }

    queryResult.value = errorResult
    executionTime.value = Date.now() - startTime
    resultCount.value = null

    // 添加到历史记录
    addToHistory(sqlQuery.value, false, Date.now() - startTime, 0, errorResult.message)

  } finally {
    executing.value = false
  }
}

/**
 * 清空SQL
 */
const clearSQL = () => {
  sqlQuery.value = ''
  queryResult.value = null
  executionTime.value = null
  resultCount.value = null
  tableData.value = []
  tableColumns.value = []
}

/**
 * 重新执行SQL
 */
const rerunSQL = (sql: string) => {
  sqlQuery.value = sql
  executeSQLQuery()
}

/**
 * 添加到历史记录
 */
const addToHistory = (sql: string, success: boolean, executionTime: number, rowCount?: number, message?: string) => {
  const historyItem: SqlHistoryItem = {
    sql,
    success,
    timestamp: new Date().toLocaleString('zh-CN'),
    executionTime,
    rowCount,
    message
  }
  
  sqlHistory.value.unshift(historyItem)
  
  // 只保留最近20条记录
  if (sqlHistory.value.length > 20) {
    sqlHistory.value = sqlHistory.value.slice(0, 20)
  }
}

// 页面初始化
onMounted(() => {
  dataSourceId.value = route.params.id as string
  dataSourceName.value = (route.query.name as string) || '未知数据源'

  // 优先从Pinia store获取SQL内容
  const executionContext = sqlExecutorStore.getExecutionContext()
  console.log('Pinia store执行上下文:', executionContext)

  if (executionContext.hasContent && executionContext.dataSource?.id === dataSourceId.value) {
    // 从store获取SQL
    sqlQuery.value = executionContext.sql
    console.log('从Pinia store获取SQL成功:', {
      sqlLength: executionContext.sql.length,
      fromView: executionContext.executeSource === 'view',
      viewName: executionContext.currentView?.name
    })
  } else {
    // 兼容旧的URL参数方式
    const sqlFromQuery = route.query.sql as string
    if (sqlFromQuery) {
      sqlQuery.value = sqlFromQuery
      console.log('从URL参数获取SQL:', sqlFromQuery.length, '字符')
    } else {
      console.log('没有SQL内容')
    }
  }

  // 如果有视图名称，显示在标题中
  const viewName = route.query.viewName as string
  if (viewName) {
    console.log('来自视图:', viewName)
  }

  console.log('SQL执行器初始化完成:', {
    dataSourceId: dataSourceId.value,
    dataSourceName: dataSourceName.value,
    viewName: viewName,
    hasSql: !!sqlQuery.value,
    sqlLength: sqlQuery.value.length,
    source: executionContext.executeSource
  })
})
</script>

<style scoped>
.sql-executor {
  padding: 0;
}

.page-header h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color);
}

.page-header p {
  margin: 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

:deep(.n-input__textarea) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

:deep(.n-data-table-th) {
  font-weight: 600;
}

:deep(.n-data-table-td) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}
</style>
