/**
 * 全局类型定义
 */

// ==================== 布局相关类型 ====================

export interface LayoutProps {
  sidebarWidth?: number
  collapsedWidth?: number
  showHeader?: boolean
}

// ==================== 菜单相关类型 ====================

export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
}

// ==================== 主题相关类型 ====================

export type ThemeMode = 'light' | 'dark' | 'auto'

// ==================== 后端统一返回类型 ====================

/**
 * 分页信息类型 - 对应后端 PageInfo
 */
export interface PageInfo {
  pageSize: number    // 每页大小
  pageNo: number      // 页码
  total: number       // 总记录数
  countTotal: boolean // 是否统计总数
}

/**
 * 后端统一响应类型 - 对应后端 ResponseData<T>
 */
export interface ResponseData<T = any> {
  success: boolean      // 请求是否成功
  errCode?: number      // 错误码
  message?: string      // 响应消息
  warnings?: string[]   // 警告信息列表
  data?: T             // 响应数据
  pageInfo?: PageInfo  // 分页信息
  exception?: any      // 异常信息
}

// ==================== 数据源相关类型 ====================

/**
 * 数据源配置类型
 */
export interface DataSourceConfig {
  enableSpecialSQL?: boolean
  password?: string
  serverAggregate?: boolean
  syncInterval?: string
  driverClass?: string
  dbType?: string
  enableSyncSchemas?: boolean
  user?: string
  url?: string
  properties?: Record<string, any>
}

/**
 * 数据源类型
 */
export interface DataSource {
  id: string                    // 数据源ID
  name: string                  // 数据源名称
  type: string                  // 数据源类型 (如: JDBC)
  config: string                // 配置信息JSON字符串
  orgId: string                 // 组织ID
  parentId?: string             // 父级ID
  isFolder: boolean             // 是否为文件夹
  status: number                // 状态 (1: 启用, 0: 禁用)
  index?: number                // 排序索引
  permission?: any              // 权限信息
  createBy: string              // 创建人
  createTime: string            // 创建时间
  updateBy: string              // 更新人
  updateTime: string            // 更新时间
}

/**
 * 解析后的数据源（包含解析后的配置）
 */
export interface ParsedDataSource extends Omit<DataSource, 'config'> {
  config: DataSourceConfig      // 解析后的配置对象
}

// ==================== SQL执行相关类型 ====================

/**
 * SQL执行结果列信息
 */
export interface SqlColumn {
  fmt: any                      // 格式化信息
  foreignKeys: any              // 外键信息
  name: string[]                // 列名数组
  type: string                  // 数据类型 (STRING, NUMBER, etc.)
}

/**
 * SQL执行结果数据
 */
export interface SqlExecuteResultData {
  columns: SqlColumn[]          // 列信息
  id: string                    // 结果ID
  name?: string                 // 结果名称
  pageInfo?: PageInfo           // 分页信息
  rows: any[][]                 // 行数据（二维数组）
  script: string                // 执行的SQL语句
  vizId?: string                // 可视化ID
  vizType?: string              // 可视化类型
}

/**
 * SQL执行请求参数
 */
export interface SqlExecuteRequest {
  script: string                // SQL语句
  sourceId: string              // 数据源ID
  size?: number                 // 返回结果数量限制，默认1000
  scriptType?: string           // 脚本类型，默认'SQL'
  columns?: string              // 列信息
  variables?: any[]             // 变量参数
  pageNo?: number               // 页码（用于分页）
  pageSize?: number             // 每页大小（用于分页）
}

/**
 * 处理后的表格数据行（用于前端展示）
 */
export interface TableRow {
  [key: string]: any            // 动态列数据
}

/**
 * 处理后的表格列定义（用于前端展示）
 */
export interface TableColumn {
  title: string                 // 列标题
  key: string                   // 列键名
  dataIndex: string             // 数据索引
  type: string                  // 数据类型
  width?: number                // 列宽度
}

// ==================== SQL视图相关类型 ====================

/**
 * SQL视图信息
 */
export interface SqlView {
  id: string                    // 视图ID
  name: string                  // 视图名称
  description?: string          // 视图描述
  sql?: string                  // SQL语句（兼容字段）
  script?: string               // SQL脚本（实际API返回字段）
  sourceId?: string             // 数据源ID
  sourceName?: string           // 数据源名称
  createBy?: string             // 创建人
  createTime?: string           // 创建时间
  updateBy?: string             // 更新人
  updateTime?: string           // 更新时间
  status?: number               // 状态 (1: 启用, 0: 禁用)
  tags?: string[]               // 标签
  type?: string                 // 视图类型
  config?: string               // 配置信息
  model?: string                // 模型信息
  orgId?: string                // 组织ID
  parentId?: string             // 父级ID
  isFolder?: boolean            // 是否为文件夹
  index?: number                // 排序索引
}
