import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/dashboard/Dashboard.vue'),
      meta: {
        title: '仪表板'
      }
    },
    {
      path: '/datasource',
      name: 'datasource',
      component: () => import('../views/datasource/DataSource.vue'),
      meta: {
        title: '数据源'
      }
    },
    {
      path: '/datasource/:id',
      name: 'datasource-detail',
      component: () => import('../views/datasource/DataSourceDetail.vue'),
      meta: {
        title: '数据源详情'
      }
    },
    {
      path: '/sql-executor/:id',
      name: 'sql-executor',
      component: () => import('../views/sql/SqlExecutor.vue'),
      meta: {
        title: 'SQL执行器'
      }
    },
    {
      path: '/views',
      name: 'views',
      component: () => import('../views/views/ViewList.vue'),
      meta: {
        title: '视图管理'
      }
    }
  ]
})

export default router
