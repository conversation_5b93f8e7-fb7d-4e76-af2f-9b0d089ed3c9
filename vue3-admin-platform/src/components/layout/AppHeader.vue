<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <!-- 侧边栏切换按钮 -->
      <n-button
        quaternary
        circle
        size="medium"
        @click="toggleSidebar"
      >
        <template #icon>
          <n-icon>
            <MenuIcon />
          </n-icon>
        </template>
      </n-button>

      <!-- 面包屑导航 -->
      <n-breadcrumb class="breadcrumb">
        <n-breadcrumb-item
          v-for="item in breadcrumbItems"
          :key="item.key"
          :clickable="!!item.path"
          @click="item.path && $router.push(item.path)"
        >
          {{ item.label }}
        </n-breadcrumb-item>
      </n-breadcrumb>
    </div>

    <!-- 右侧区域 -->
    <div class="header-right">
      <n-space align="center" :size="16">
        <!-- 主题切换按钮 -->
        <n-button
          quaternary
          circle
          size="medium"
          @click="toggleTheme"
        >
          <template #icon>
            <n-icon>
              <SunnyIcon v-if="isDark" />
              <MoonIcon v-else />
            </n-icon>
          </template>
        </n-button>

        <!-- 用户信息下拉菜单 -->
        <n-dropdown
          :options="userMenuOptions"
          @select="handleUserMenuSelect"
        >
          <div class="user-info">
            <n-avatar
              round
              size="medium"
              :src="userInfo.avatar"
              :fallback-src="defaultAvatar"
            >
              {{ userInfo.username.charAt(0).toUpperCase() }}
            </n-avatar>
            <span class="username">{{ userInfo.username }}</span>
            <n-icon>
              <ChevronDownIcon />
            </n-icon>
          </div>
        </n-dropdown>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NButton,
  NIcon,
  NBreadcrumb,
  NBreadcrumbItem,
  NDropdown,
  NAvatar,
  NSpace
} from 'naive-ui'
import {
  MenuOutline as MenuIcon,
  SunnyOutline as SunnyIcon,
  MoonOutline as MoonIcon,
  ChevronDownOutline as ChevronDownIcon,
  PersonOutline as PersonIcon,
  SettingsOutline as SettingsIcon,
  LogOutOutline as LogoutIcon
} from '@vicons/ionicons5'
import { useThemeStore } from '@/stores/theme'
import type { UserInfo } from '@/types'

/**
 * 头部组件
 * 包含面包屑导航、用户信息显示、主题切换按钮、侧边栏切换按钮
 */

// 路由相关
const router = useRouter()
const route = useRoute()

// 主题状态
const themeStore = useThemeStore()
const { isDark } = themeStore

// 用户信息 - 从store或API获取
const userInfo: UserInfo = {
  id: '',
  username: '',
  email: '',
  avatar: '',
  role: ''
}

// 默认头像
const defaultAvatar = ''

// 渲染图标的辅助函数
const renderIcon = (icon: any) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

// 用户菜单选项
const userMenuOptions = [
  {
    key: 'profile',
    label: '个人资料',
    icon: renderIcon(PersonIcon)
  },
  {
    key: 'settings',
    label: '设置',
    icon: renderIcon(SettingsIcon)
  },
  {
    type: 'divider'
  },
  {
    key: 'logout',
    label: '退出登录',
    icon: renderIcon(LogoutIcon)
  }
]

// 面包屑导航项
const breadcrumbItems = computed(() => {
  const path = route.path
  const items = []

  // 根据路径生成面包屑
  if (path.startsWith('/dashboard')) {
    items.push({ key: 'dashboard', label: '仪表板', path: '/dashboard' })
  } else if (path === '/conversation') {
    items.push({ key: 'conversation', label: '对话管理' })
    items.push({ key: 'conversation-advanced', label: '高级管理', path: '/conversation' })
  } else if (path === '/simple-conversation') {
    items.push({ key: 'conversation', label: '对话管理' })
    items.push({ key: 'conversation-simple', label: '简易管理', path: '/simple-conversation' })
  } else if (path === '/database-connections') {
    items.push({ key: 'sql-tools', label: 'SQL工具' })
    items.push({ key: 'database-connections', label: '数据库连接', path: '/database-connections' })
  } else if (path === '/sql-manager') {
    items.push({ key: 'sql-tools', label: 'SQL工具' })
    items.push({ key: 'sql-manager', label: 'SQL管理工具', path: '/sql-manager' })
  } else if (path === '/operator-query') {
    items.push({ key: 'sql-tools', label: 'SQL工具' })
    items.push({ key: 'operator-query', label: '数据查询中心', path: '/operator-query' })
  }

  return items
})

/**
 * 切换侧边栏
 */
const toggleSidebar = () => {
  themeStore.toggleSidebar()
}

/**
 * 切换主题
 */
const toggleTheme = () => {
  themeStore.toggleTheme()
}

/**
 * 处理用户菜单选择
 */
const handleUserMenuSelect = (key: string) => {
  switch (key) {
    case 'profile':
      // 处理个人资料逻辑
      console.log('个人资料')
      break
    case 'settings':
      // 处理设置逻辑
      console.log('设置')
      break
    case 'logout':
      // 处理退出登录逻辑
      console.log('退出登录')
      break
  }
}
</script>

<style scoped>
.header {
  height: 100%;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.breadcrumb {
  margin-left: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: var(--n-color-hover);
}

.username {
  font-size: 14px;
  color: var(--n-text-color);
}
</style>
