<template>
  <div class="sidebar">
    <!-- Logo区域 -->
    <div class="logo-container" :class="{ collapsed: sidebarCollapsed }">
      <div class="logo">
        <n-icon size="32" color="#18a058">
          <LogoIcon />
        </n-icon>
        <span v-if="!sidebarCollapsed" class="logo-text">Admin Platform</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <div class="menu-container">
      <n-menu
        :collapsed="sidebarCollapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="activeKey"
        @update:value="handleMenuSelect"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NMenu, NIcon } from 'naive-ui'
import { 
  HomeOutline as HomeIcon,
  PersonOutline as UserIcon,
  SettingsOutline as SettingsIcon,
  StatsChartOutline as DashboardIcon,
  DocumentTextOutline as DocumentIcon
} from '@vicons/ionicons5'
import { useThemeStore } from '@/stores/theme'
import type { MenuItem } from '@/types'

/**
 * 侧边栏组件
 * 支持折叠/展开功能，多级菜单，图标和文字显示
 */

// 路由相关
const router = useRouter()
const route = useRoute()

// 主题状态
const themeStore = useThemeStore()
const { sidebarCollapsed } = themeStore

// Logo图标组件
const LogoIcon = () => h(DashboardIcon)

// 渲染图标的辅助函数
const renderIcon = (icon: any) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

// 菜单配置 - 使用Naive UI的MenuOption格式
const menuOptions = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: renderIcon(DashboardIcon)
  },
  {
    key: 'data-management',
    label: '数据管理',
    icon: renderIcon(SettingsIcon),
    children: [
      {
        key: 'datasource',
        label: '数据源管理'
      },
      {
        key: 'views',
        label: '视图管理'
      }
    ]
  }
]

// 菜单项到路径的映射
const menuPathMap: Record<string, string> = {
  'dashboard': '/dashboard',
  'datasource': '/datasource',
  'views': '/views'
}

// 当前激活的菜单项
const activeKey = computed(() => {
  const path = route.path
  // 根据当前路径匹配菜单项
  if (path === '/datasource') {
    return 'datasource'
  } else if (path === '/views') {
    return 'views'
  }
  return 'dashboard'
})

/**
 * 处理菜单选择
 */
const handleMenuSelect = (key: string) => {
  const path = menuPathMap[key]

  if (path) {
    router.push(path)
  }
}
</script>

<style scoped>
.sidebar {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--n-border-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.logo-container.collapsed {
  padding: 0 16px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color);
  white-space: nowrap;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
