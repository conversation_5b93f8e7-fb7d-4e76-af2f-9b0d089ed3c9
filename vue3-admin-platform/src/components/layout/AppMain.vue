<template>
  <div class="main-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <n-spin size="large">
        <template #description>
          页面加载中...
        </template>
      </n-spin>
    </div>

    <!-- 路由视图容器 -->
    <router-view v-else v-slot="{ Component, route }">
      <transition
        :name="transitionName"
        mode="out-in"
        @before-enter="onBeforeEnter"
        @after-enter="onAfterEnter"
      >
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NSpin } from 'naive-ui'

/**
 * 主内容区组件
 * 路由视图容器，页面过渡动画，加载状态处理
 */

// 路由相关
const router = useRouter()
const route = useRoute()

// 加载状态
const loading = ref(false)

// 过渡动画名称
const transitionName = computed(() => {
  // 可以根据路由变化决定不同的过渡效果
  return 'fade'
})

/**
 * 进入动画前的处理
 */
const onBeforeEnter = () => {
  loading.value = false
}

/**
 * 进入动画后的处理
 */
const onAfterEnter = () => {
  // 动画完成后的处理
}

// 监听路由变化，显示加载状态
watch(
  () => route.path,
  () => {
    loading.value = true
    // 模拟加载延迟
    setTimeout(() => {
      loading.value = false
    }, 300)
  },
  { immediate: false }
)
</script>

<style scoped>
.main-container {
  height: 100%;
  position: relative;
}

.loading-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(30px);
  opacity: 0;
}
</style>
