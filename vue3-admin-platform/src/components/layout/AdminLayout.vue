<template>
  <n-config-provider :theme="isDark ? darkTheme : null">
    <n-message-provider>
      <n-layout class="admin-layout" has-sider>
      <!-- 侧边栏 -->
      <n-layout-sider
        :collapsed="sidebarCollapsed"
        :collapsed-width="collapsedWidth"
        :width="sidebarWidth"
        :native-scrollbar="false"
        show-trigger="arrow-circle"
        collapse-mode="width"
        bordered
        @collapse="handleSidebarCollapse"
        @expand="handleSidebarExpand"
      >
        <AppSidebar />
      </n-layout-sider>

      <!-- 主内容区 -->
      <n-layout>
        <!-- 头部 -->
        <n-layout-header v-if="showHeader" bordered class="header">
          <AppHeader />
        </n-layout-header>

        <!-- 内容区域 -->
        <n-layout-content class="content">
          <AppMain />
        </n-layout-content>
      </n-layout>
    </n-layout>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  NConfigProvider,
  NMessageProvider,
  NLayout,
  NLayoutSider,
  NLayoutHeader,
  NLayoutContent,
  darkTheme
} from 'naive-ui'
import { useThemeStore } from '@/stores/theme'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'
import AppMain from './AppMain.vue'
import type { LayoutProps } from '@/types'

/**
 * 管理平台主布局组件
 * 实现左右布局结构：侧边栏 + 主内容区
 */

// Props定义
interface Props extends LayoutProps {
  sidebarWidth?: number
  collapsedWidth?: number
  showHeader?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  sidebarWidth: 240,
  collapsedWidth: 64,
  showHeader: true
})

// 主题状态
const themeStore = useThemeStore()
const { sidebarCollapsed, isDark } = themeStore

// 计算属性
const sidebarWidth = computed(() => props.sidebarWidth)
const collapsedWidth = computed(() => props.collapsedWidth)
const showHeader = computed(() => props.showHeader)

/**
 * 处理侧边栏折叠
 */
const handleSidebarCollapse = () => {
  themeStore.toggleSidebar()
}

/**
 * 处理侧边栏展开
 */
const handleSidebarExpand = () => {
  themeStore.toggleSidebar()
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.header {
  height: 64px;
  padding: 0;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.content {
  padding: 16px;
  overflow: auto;
  height: calc(100vh - 64px);
}
</style>
