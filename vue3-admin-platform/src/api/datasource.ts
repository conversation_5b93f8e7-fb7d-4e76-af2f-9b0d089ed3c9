/**
 * DataSource API 服务
 * 获取数据源列表
 */

import request from '@/utils/request'
import { getCommonHeaders, getCommonParams } from './config'
import type { ResponseData, DataSource, SqlExecuteRequest, SqlExecuteResultData, SqlView } from '@/types'

/**
 * 获取数据源列表
 */
export async function getDataSources(): Promise<ResponseData<DataSource[]>> {
  return request.get<DataSource[]>(
    '/api/v1/sources',
    getCommonParams(),
    {
      headers: getCommonHeaders()
    }
  )
}



/**
 * 执行SQL查询
 */
export async function executeSQL(params: SqlExecuteRequest): Promise<ResponseData<SqlExecuteResultData>> {
  const requestData = {
    script: params.script,
    sourceId: params.sourceId,
    size: params.size || 1000,
    scriptType: params.scriptType || 'SQL',
    columns: params.columns || '',
    variables: params.variables || []
  }

  return request.post<SqlExecuteResultData>(
    '/api/v1/data-provider/execute/test',
    requestData,
    {
      headers: getCommonHeaders()
    }
  )
}

/**
 * 获取视图列表
 */
export async function getViews(): Promise<ResponseData<SqlView[]>> {
  return request.get<SqlView[]>(
    '/api/v1/views',
    getCommonParams(),
    {
      headers: getCommonHeaders()
    }
  )
}

/**
 * 根据ID获取视图详情
 */
export async function getViewById(viewId: string): Promise<ResponseData<SqlView>> {
  console.log('调用getViewById API, viewId:', viewId)

  const response = await request.get<SqlView>(
    `/api/v1/views/${viewId}`,
    getCommonParams(),
    {
      headers: getCommonHeaders()
    }
  )

  console.log('getViewById API响应:', response)
  console.log('响应数据:', response.data)

  return response
}
