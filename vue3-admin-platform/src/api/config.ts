/**
 * API配置文件
 * 统一管理API相关的配置信息
 */

/**
 * API配置常量
 */
export const API_CONFIG = {
  // 基础URL
  BASE_URL: 'http://127.0.0.1:8081/api/v1',
  
  // 组织ID
  ORG_ID: '65324b93caa64747a3ebced6ae09c3de',
  
  // 认证Token
  AUTH_TOKEN:'Bearer eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxMTExIiwicGFzc3dvcmQiOjMzNzA4MTI0OSwiZXhwIjoxNzUzODUzNDY1fQ.T-QivikdKtKyZGjE9vRyuHMglO1JbHdchjtFBlSgsAc',
  
  // 请求超时时间
  TIMEOUT: 30000
}

/**
 * 获取通用请求头
 */
export function getCommonHeaders(): Record<string, string> {
  return {
    'Authorization': API_CONFIG.AUTH_TOKEN,
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN',
    'Content-Type': 'application/json'
  }
}

/**
 * 获取通用查询参数
 */
export function getCommonParams(): Record<string, string> {
  return {
    orgId: API_CONFIG.ORG_ID
  }
}

/**
 * 构建完整的API URL
 */
export function buildApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

/**
 * 更新认证Token
 */
export function updateAuthToken(newToken: string): void {
  API_CONFIG.AUTH_TOKEN = newToken.startsWith('Bearer ') ? newToken : `Bearer ${newToken}`
}

/**
 * 获取当前认证Token
 */
export function getAuthToken(): string {
  return API_CONFIG.AUTH_TOKEN
}

/**
 * 更新组织ID
 */
export function updateOrgId(newOrgId: string): void {
  API_CONFIG.ORG_ID = newOrgId
}

/**
 * 获取当前组织ID
 */
export function getOrgId(): string {
  return API_CONFIG.ORG_ID
}

/**
 * 调试函数：打印当前配置信息
 */
export function debugConfig(): void {
  console.log('=== API配置调试信息 ===')
  console.log('当前Token:', API_CONFIG.AUTH_TOKEN)
  console.log('组织ID:', API_CONFIG.ORG_ID)
  console.log('基础URL:', API_CONFIG.BASE_URL)
  console.log('Token最后几位:', API_CONFIG.AUTH_TOKEN.slice(-10))
  console.log('========================')
}
