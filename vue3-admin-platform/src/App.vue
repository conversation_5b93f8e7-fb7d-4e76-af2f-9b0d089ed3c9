<script setup lang="ts">
import { onMounted } from 'vue'
import AdminLayout from '@/components/layout/AdminLayout.vue'
import { useThemeStore } from '@/stores/theme'

// 初始化主题
const themeStore = useThemeStore()

onMounted(() => {
  themeStore.initTheme()
})
</script>

<template>
  <AdminLayout />
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

#app {
  height: 100%;
  width: 100%;
  position: relative;
}

/* 确保Naive UI的布局组件正确全屏 */
.n-layout {
  height: 100%;
}

.n-layout-sider {
  height: 100vh !important;
}
</style>
